-- 初始化数据脚本（可选）

-- 这个文件可以为空，因为我们使用DataInitializer类来初始化数据
-- 如果需要插入一些基础数据，可以在这里添加INSERT语句

-- 示例：插入一些示例图书数据
INSERT IGNORE INTO books (id, title, author, category, description, cover_image, status) VALUES
(1, '活着', '余华', '文学经典', '一个关于生命意义的深刻故事', '', 1),
(2, '三体', '刘慈欣', '科幻小说', '科幻文学的经典之作', '', 1),
(3, '人类简史', '尤瓦尔·赫拉利', '历史人文', '从动物到上帝的人类发展史', '', 1),
(4, '原则', '瑞·达利欧', '商业管理', '生活和工作的原则', '', 1),
(5, '心理学与生活', '理查德·格里格', '心理成长', '心理学入门经典教材', '', 1);
